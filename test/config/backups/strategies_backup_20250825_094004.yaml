evolution_config:
  base_templates:
  - RSI_Mean_Reversion_Base
  - EMA_Crossover_Base
  - Bollinger_Bounce_Base
  - VWAP_Breakout_Base
  max_strategies_per_stock_timeframe: 3
  min_performance_threshold: 0.05
  ranking_threshold: 80
  target_stocks:
  - 360ONE
  - ABB
  - RELIANCE
  - TCS
  - INFY
  - HDFC
  - ICICI
  - SBI
  target_timeframes:
  - 1min
  - 3min
  - 5min
  - 15min
  - 30min
  - 1h
strategies:
- description: RSI-based mean reversion strategy
  entry_long: RSI_14 < 30 & Volume > Volume.rolling(20).mean()
  entry_short: RSI_14 > 70 & Volume > Volume.rolling(20).mean()
  exit_long: RSI_14 > 50 | Close < (Entry * (1 - stop_loss_pct))
  exit_short: RSI_14 < 50 | Close > (Entry * (1 + stop_loss_pct))
  name: RSI_Mean_Reversion_Base
  parameters:
    rsi_period: 14
    volume_multiplier: 1.0
    volume_period: 20
  position_sizing:
    method: fixed_percentage
    percentage: 0.05
  ranking: 100
  risk_management:
    max_positions: 3
    stop_loss_pct: 0.02
    take_profit_pct: 0.04
  type: base_template
- description: EMA crossover trend following strategy
  entry_long: EMA_5 > EMA_20 & EMA_20 > EMA_50 & Volume > Volume.rolling(20).mean()
  entry_short: EMA_5 < EMA_20 & EMA_20 < EMA_50 & Volume > Volume.rolling(20).mean()
  exit_long: EMA_5 < EMA_20 | Close < (Entry * (1 - stop_loss_pct))
  exit_short: EMA_5 > EMA_20 | Close > (Entry * (1 + stop_loss_pct))
  name: EMA_Crossover_Base
  parameters:
    ema_fast: 5
    ema_slow: 20
    ema_trend: 50
    volume_period: 20
  position_sizing:
    method: fixed_percentage
    percentage: 0.04
  ranking: 95
  risk_management:
    max_positions: 2
    stop_loss_pct: 0.025
    take_profit_pct: 0.05
  type: base_template
- best_risk_reward: 2.25
  entry_long: RSI_14 < 28 & Volume > Volume.rolling(25).mean() * 1.5
  entry_short: RSI_14 > 72 & Volume > Volume.rolling(25).mean() * 1.5
  exit_long: RSI_14 > 55 | Close < (Entry * 0.98)
  exit_short: RSI_14 < 45 | Close > (Entry * 1.02)
  name: RSI_Mean_Reversion_ABB_5min
  parameters:
    rsi_period: 14
    volume_multiplier: 1.5
    volume_period: 25
  parent_template: RSI_Mean_Reversion_Base
  performance_summary:
    max_drawdown: 12.0
    profit_factor: 1.5
    sharpe_ratio: 1.4
    total_return: 10.2
    win_rate: 0.68
  position_sizing:
    method: fixed_percentage
    percentage: 0.04
  ranking: 92
  risk_management:
    max_positions: 3
    stop_loss_pct: 0.02
    take_profit_pct: 0.045
  target_stock: ABB
  target_timeframe: 5min
  type: evolved_strategy
- description: Bollinger Bands bounce strategy
  entry_long: Close < BB_Lower & RSI_14 < 40
  entry_short: Close > BB_Upper & RSI_14 > 60
  exit_long: Close > BB_Middle | Close < (Entry * (1 - stop_loss_pct))
  exit_short: Close < BB_Middle | Close > (Entry * (1 + stop_loss_pct))
  name: Bollinger_Bounce_Base
  parameters:
    bb_period: 20
    bb_std: 2.0
    rsi_period: 14
  position_sizing:
    method: fixed_percentage
    percentage: 0.06
  ranking: 90
  risk_management:
    max_positions: 2
    stop_loss_pct: 0.03
    take_profit_pct: 0.06
  type: base_template
- best_risk_reward: 2.0
  entry_long: Close < BB_Lower & RSI_14 < 35 & Volume > Volume.rolling(15).mean()
    * 1.2
  entry_short: Close > BB_Upper & RSI_14 > 65 & Volume > Volume.rolling(15).mean()
    * 1.2
  exit_long: Close > BB_Middle | Close < (Entry * 0.975)
  exit_short: Close < BB_Middle | Close > (Entry * 1.025)
  name: Bollinger_Bounce_360ONE_1min
  parameters:
    bb_period: 18
    bb_std: 2.1
    rsi_period: 12
    volume_multiplier: 1.2
    volume_period: 15
  parent_template: Bollinger_Bounce_Base
  performance_summary:
    max_drawdown: 15.0
    profit_factor: 1.3
    sharpe_ratio: 1.2
    total_return: 8.5
    win_rate: 0.65
  position_sizing:
    method: fixed_percentage
    percentage: 0.05
  ranking: 89
  risk_management:
    max_positions: 2
    stop_loss_pct: 0.025
    take_profit_pct: 0.05
  target_stock: 360ONE
  target_timeframe: 1min
  type: evolved_strategy
- best_risk_reward: 2.2
  entry_long: EMA_8 > EMA_21 & EMA_21 > EMA_55 & Volume > Volume.rolling(18).mean()
    * 1.3
  entry_short: EMA_8 < EMA_21 & EMA_21 < EMA_55 & Volume > Volume.rolling(18).mean()
    * 1.3
  exit_long: EMA_8 < EMA_21 | Close < (Entry * 0.975)
  exit_short: EMA_8 > EMA_21 | Close > (Entry * 1.025)
  name: EMA_Crossover_RELIANCE_3min
  parameters:
    ema_fast: 8
    ema_slow: 21
    ema_trend: 55
    volume_multiplier: 1.3
    volume_period: 18
  parent_template: EMA_Crossover_Base
  performance_summary:
    max_drawdown: 18.0
    profit_factor: 1.25
    sharpe_ratio: 1.1
    total_return: 7.8
    win_rate: 0.62
  position_sizing:
    method: fixed_percentage
    percentage: 0.045
  ranking: 87
  risk_management:
    max_positions: 2
    stop_loss_pct: 0.025
    take_profit_pct: 0.055
  target_stock: RELIANCE
  target_timeframe: 3min
  type: evolved_strategy
- description: VWAP breakout strategy
  entry_long: Close > VWAP * 1.002 & Volume > Volume.rolling(20).mean() * 1.5
  entry_short: Close < VWAP * 0.998 & Volume > Volume.rolling(20).mean() * 1.5
  exit_long: Close < VWAP | Close < (Entry * (1 - stop_loss_pct))
  exit_short: Close > VWAP | Close > (Entry * (1 + stop_loss_pct))
  name: VWAP_Breakout_Base
  parameters:
    volume_multiplier: 1.5
    volume_period: 20
    vwap_deviation: 0.002
  position_sizing:
    method: fixed_percentage
    percentage: 0.05
  ranking: 85
  risk_management:
    max_positions: 3
    stop_loss_pct: 0.02
    take_profit_pct: 0.04
  type: base_template
- best_risk_reward: &id001
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 75
    oversold_threshold: 25
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:16:19'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 41936
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.015
    take_profit: 0.03
  risk_reward_ratios:
  - *id001
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id002
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 70
    oversold_threshold: 30
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:16:19'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 41936
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.02
    take_profit: 0.04
  risk_reward_ratios:
  - *id002
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id003
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 65
    oversold_threshold: 35
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:16:19'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 41936
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.025
    take_profit: 0.05
  risk_reward_ratios:
  - *id003
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id004
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 75
    oversold_threshold: 25
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:16:19'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 64637
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.015
    take_profit: 0.03
  risk_reward_ratios:
  - *id004
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id005
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 70
    oversold_threshold: 30
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:16:19'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 64637
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.02
    take_profit: 0.04
  risk_reward_ratios:
  - *id005
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id006
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 65
    oversold_threshold: 35
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:16:19'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 64637
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.025
    take_profit: 0.05
  risk_reward_ratios:
  - *id006
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id007
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 75
    oversold_threshold: 25
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:16:19'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 29958
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.015
    take_profit: 0.03
  risk_reward_ratios:
  - *id007
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id008
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 70
    oversold_threshold: 30
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:16:19'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 29958
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.02
    take_profit: 0.04
  risk_reward_ratios:
  - *id008
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id009
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 75
    oversold_threshold: 25
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:16:21'
  market_regime: neutral
  name: EMA_Crossover_Base_Crossover_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 34279
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.015
    take_profit: 0.03
  risk_reward_ratios:
  - *id009
  stock_name: Crossover
  timeframe:
  - 1min
- best_risk_reward: &id010
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 70
    oversold_threshold: 30
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:16:21'
  market_regime: neutral
  name: EMA_Crossover_Base_Crossover_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 34279
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.02
    take_profit: 0.04
  risk_reward_ratios:
  - *id010
  stock_name: Crossover
  timeframe:
  - 1min
- best_risk_reward: &id011
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 65
    oversold_threshold: 35
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:16:21'
  market_regime: neutral
  name: EMA_Crossover_Base_Crossover_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 34279
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.025
    take_profit: 0.05
  risk_reward_ratios:
  - *id011
  stock_name: Crossover
  timeframe:
  - 1min
- best_risk_reward: &id012
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 75
    oversold_threshold: 25
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:16:21'
  market_regime: neutral
  name: EMA_Crossover_Base_Crossover_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 22474
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.015
    take_profit: 0.03
  risk_reward_ratios:
  - *id012
  stock_name: Crossover
  timeframe:
  - 1min
- best_risk_reward: &id013
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 70
    oversold_threshold: 30
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:16:21'
  market_regime: neutral
  name: EMA_Crossover_Base_Crossover_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 22474
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.02
    take_profit: 0.04
  risk_reward_ratios:
  - *id013
  stock_name: Crossover
  timeframe:
  - 1min
- best_risk_reward: &id014
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 65
    oversold_threshold: 35
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:16:21'
  market_regime: neutral
  name: EMA_Crossover_Base_Crossover_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 22474
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.025
    take_profit: 0.05
  risk_reward_ratios:
  - *id014
  stock_name: Crossover
  timeframe:
  - 1min
- best_risk_reward: &id015
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 75
    oversold_threshold: 25
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:16:21'
  market_regime: neutral
  name: EMA_Crossover_Base_Crossover_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 33352
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.015
    take_profit: 0.03
  risk_reward_ratios:
  - *id015
  stock_name: Crossover
  timeframe:
  - 1min
- best_risk_reward: &id016
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 70
    oversold_threshold: 30
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:16:21'
  market_regime: neutral
  name: EMA_Crossover_Base_Crossover_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 33352
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.02
    take_profit: 0.04
  risk_reward_ratios:
  - *id016
  stock_name: Crossover
  timeframe:
  - 1min
- best_risk_reward: &id017
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 75
    oversold_threshold: 25
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:16:51'
  market_regime: neutral
  name: RSI_Mean_Reversion_ABB_5min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 41936
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.015
    take_profit: 0.03
  risk_reward_ratios:
  - *id017
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id018
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 70
    oversold_threshold: 30
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:16:51'
  market_regime: neutral
  name: RSI_Mean_Reversion_ABB_5min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 41936
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.02
    take_profit: 0.04
  risk_reward_ratios:
  - *id018
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id019
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 65
    oversold_threshold: 35
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:16:51'
  market_regime: neutral
  name: RSI_Mean_Reversion_ABB_5min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 41936
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.025
    take_profit: 0.05
  risk_reward_ratios:
  - *id019
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id020
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 75
    oversold_threshold: 25
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:16:51'
  market_regime: neutral
  name: RSI_Mean_Reversion_ABB_5min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 29958
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.015
    take_profit: 0.03
  risk_reward_ratios:
  - *id020
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id021
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 70
    oversold_threshold: 30
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:16:51'
  market_regime: neutral
  name: RSI_Mean_Reversion_ABB_5min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 29958
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.02
    take_profit: 0.04
  risk_reward_ratios:
  - *id021
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id022
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 65
    oversold_threshold: 35
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:16:51'
  market_regime: neutral
  name: RSI_Mean_Reversion_ABB_5min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 29958
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.025
    take_profit: 0.05
  risk_reward_ratios:
  - *id022
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id023
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 75
    oversold_threshold: 25
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:16:51'
  market_regime: neutral
  name: RSI_Mean_Reversion_ABB_5min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 64637
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.015
    take_profit: 0.03
  risk_reward_ratios:
  - *id023
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id024
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 70
    oversold_threshold: 30
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:16:51'
  market_regime: neutral
  name: RSI_Mean_Reversion_ABB_5min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 64637
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.02
    take_profit: 0.04
  risk_reward_ratios:
  - *id024
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id025
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 75
    oversold_threshold: 25
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:17:27'
  market_regime: neutral
  name: EMA_Crossover_RELIANCE_3min_Crossover_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 33352
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.015
    take_profit: 0.03
  risk_reward_ratios:
  - *id025
  stock_name: Crossover
  timeframe:
  - 1min
- best_risk_reward: &id026
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 70
    oversold_threshold: 30
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:17:27'
  market_regime: neutral
  name: EMA_Crossover_RELIANCE_3min_Crossover_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 33352
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.02
    take_profit: 0.04
  risk_reward_ratios:
  - *id026
  stock_name: Crossover
  timeframe:
  - 1min
- best_risk_reward: &id027
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 65
    oversold_threshold: 35
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:17:27'
  market_regime: neutral
  name: EMA_Crossover_RELIANCE_3min_Crossover_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 33352
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.025
    take_profit: 0.05
  risk_reward_ratios:
  - *id027
  stock_name: Crossover
  timeframe:
  - 1min
- best_risk_reward: &id028
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 75
    oversold_threshold: 25
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:17:27'
  market_regime: neutral
  name: EMA_Crossover_RELIANCE_3min_Crossover_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 34279
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.015
    take_profit: 0.03
  risk_reward_ratios:
  - *id028
  stock_name: Crossover
  timeframe:
  - 1min
- best_risk_reward: &id029
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 70
    oversold_threshold: 30
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:17:27'
  market_regime: neutral
  name: EMA_Crossover_RELIANCE_3min_Crossover_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 34279
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.02
    take_profit: 0.04
  risk_reward_ratios:
  - *id029
  stock_name: Crossover
  timeframe:
  - 1min
- best_risk_reward: &id030
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 65
    oversold_threshold: 35
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:17:27'
  market_regime: neutral
  name: EMA_Crossover_RELIANCE_3min_Crossover_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 34279
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.025
    take_profit: 0.05
  risk_reward_ratios:
  - *id030
  stock_name: Crossover
  timeframe:
  - 1min
- best_risk_reward: &id031
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 75
    oversold_threshold: 25
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:17:27'
  market_regime: neutral
  name: EMA_Crossover_RELIANCE_3min_Crossover_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 22474
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.015
    take_profit: 0.03
  risk_reward_ratios:
  - *id031
  stock_name: Crossover
  timeframe:
  - 1min
- best_risk_reward: &id032
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 70
    oversold_threshold: 30
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:17:27'
  market_regime: neutral
  name: EMA_Crossover_RELIANCE_3min_Crossover_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 22474
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.02
    take_profit: 0.04
  risk_reward_ratios:
  - *id032
  stock_name: Crossover
  timeframe:
  - 1min
- best_risk_reward: &id033
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 75
    oversold_threshold: 25
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:22:52'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 64637
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.015
    take_profit: 0.03
  risk_reward_ratios:
  - *id033
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id034
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 70
    oversold_threshold: 30
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:22:52'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 64637
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.02
    take_profit: 0.04
  risk_reward_ratios:
  - *id034
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id035
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 65
    oversold_threshold: 35
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:22:52'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 64637
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.025
    take_profit: 0.05
  risk_reward_ratios:
  - *id035
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id036
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 75
    oversold_threshold: 25
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:22:52'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 29958
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.015
    take_profit: 0.03
  risk_reward_ratios:
  - *id036
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id037
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 70
    oversold_threshold: 30
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:22:52'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 29958
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.02
    take_profit: 0.04
  risk_reward_ratios:
  - *id037
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id038
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 65
    oversold_threshold: 35
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:22:52'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 29958
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.025
    take_profit: 0.05
  risk_reward_ratios:
  - *id038
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id039
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 75
    oversold_threshold: 25
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:22:52'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 41936
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.015
    take_profit: 0.03
  risk_reward_ratios:
  - *id039
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id040
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 70
    oversold_threshold: 30
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:22:52'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 41936
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.02
    take_profit: 0.04
  risk_reward_ratios:
  - *id040
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id041
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 75
    oversold_threshold: 25
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:23:54'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 29958
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.015
    take_profit: 0.03
  risk_reward_ratios:
  - *id041
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id042
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 70
    oversold_threshold: 30
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:23:54'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 29958
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.02
    take_profit: 0.04
  risk_reward_ratios:
  - *id042
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id043
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 65
    oversold_threshold: 35
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:23:54'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 29958
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.025
    take_profit: 0.05
  risk_reward_ratios:
  - *id043
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id044
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 75
    oversold_threshold: 25
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:23:54'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 41936
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.015
    take_profit: 0.03
  risk_reward_ratios:
  - *id044
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id045
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 70
    oversold_threshold: 30
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:23:54'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 41936
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.02
    take_profit: 0.04
  risk_reward_ratios:
  - *id045
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id046
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 65
    oversold_threshold: 35
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:23:54'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 41936
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.025
    take_profit: 0.05
  risk_reward_ratios:
  - *id046
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id047
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 75
    oversold_threshold: 25
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:23:54'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 64637
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.015
    take_profit: 0.03
  risk_reward_ratios:
  - *id047
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id048
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 70
    oversold_threshold: 30
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:23:54'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 64637
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.02
    take_profit: 0.04
  risk_reward_ratios:
  - *id048
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id049
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 75
    oversold_threshold: 25
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:23:59'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 41936
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.015
    take_profit: 0.03
  risk_reward_ratios:
  - *id049
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id050
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 70
    oversold_threshold: 30
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:23:59'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 41936
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.02
    take_profit: 0.04
  risk_reward_ratios:
  - *id050
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id051
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 65
    oversold_threshold: 35
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:23:59'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 41936
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.025
    take_profit: 0.05
  risk_reward_ratios:
  - *id051
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id052
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 75
    oversold_threshold: 25
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:23:59'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 64637
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.015
    take_profit: 0.03
  risk_reward_ratios:
  - *id052
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id053
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 70
    oversold_threshold: 30
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:23:59'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 64637
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.02
    take_profit: 0.04
  risk_reward_ratios:
  - *id053
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id054
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 65
    oversold_threshold: 35
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:23:59'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 64637
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.025
    take_profit: 0.05
  risk_reward_ratios:
  - *id054
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id055
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 75
    oversold_threshold: 25
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:23:59'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 29958
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.015
    take_profit: 0.03
  risk_reward_ratios:
  - *id055
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id056
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 70
    oversold_threshold: 30
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:23:59'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 29958
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.02
    take_profit: 0.04
  risk_reward_ratios:
  - *id056
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id057
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 75
    oversold_threshold: 25
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:25:00'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 64637
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.015
    take_profit: 0.03
  risk_reward_ratios:
  - *id057
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id058
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 70
    oversold_threshold: 30
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:25:00'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 64637
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.02
    take_profit: 0.04
  risk_reward_ratios:
  - *id058
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id059
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 65
    oversold_threshold: 35
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:25:00'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 64637
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.025
    take_profit: 0.05
  risk_reward_ratios:
  - *id059
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id060
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 75
    oversold_threshold: 25
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:25:00'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 29958
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.015
    take_profit: 0.03
  risk_reward_ratios:
  - *id060
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id061
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 70
    oversold_threshold: 30
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:25:00'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 29958
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.02
    take_profit: 0.04
  risk_reward_ratios:
  - *id061
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id062
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 65
    oversold_threshold: 35
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:25:00'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 29958
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.025
    take_profit: 0.05
  risk_reward_ratios:
  - *id062
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id063
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 75
    oversold_threshold: 25
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:25:00'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 41936
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.015
    take_profit: 0.03
  risk_reward_ratios:
  - *id063
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id064
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 70
    oversold_threshold: 30
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:25:00'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 41936
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.02
    take_profit: 0.04
  risk_reward_ratios:
  - *id064
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id065
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 75
    oversold_threshold: 25
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:25:06'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 64637
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.015
    take_profit: 0.03
  risk_reward_ratios:
  - *id065
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id066
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 70
    oversold_threshold: 30
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:25:06'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 64637
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.02
    take_profit: 0.04
  risk_reward_ratios:
  - *id066
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id067
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 65
    oversold_threshold: 35
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:25:06'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 64637
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.025
    take_profit: 0.05
  risk_reward_ratios:
  - *id067
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id068
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 75
    oversold_threshold: 25
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:25:06'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 41936
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.015
    take_profit: 0.03
  risk_reward_ratios:
  - *id068
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id069
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 70
    oversold_threshold: 30
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:25:06'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 41936
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.02
    take_profit: 0.04
  risk_reward_ratios:
  - *id069
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id070
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 65
    oversold_threshold: 35
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:25:06'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 41936
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.025
    take_profit: 0.05
  risk_reward_ratios:
  - *id070
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id071
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 75
    oversold_threshold: 25
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:25:06'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 29958
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.015
    take_profit: 0.03
  risk_reward_ratios:
  - *id071
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id072
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 70
    oversold_threshold: 30
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:25:06'
  market_regime: neutral
  name: RSI_Mean_Reversion_Base_Mean_1min_Mean_1min
  performance_summary:
    max_drawdown: .nan
    sharpe_ratio: .nan
    total_trades: 29958
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 75
  risk_management:
    stop_loss: 0.02
    take_profit: 0.04
  risk_reward_ratios:
  - *id072
  stock_name: Mean
  timeframe:
  - 1min
- best_risk_reward: &id073
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 75
    oversold_threshold: 25
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:16:53'
  market_regime: neutral
  name: Bollinger_Bounce_Base_Bounce_1min
  performance_summary:
    max_drawdown: 0.0
    sharpe_ratio: 0.0
    total_trades: 0
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 50
  risk_management:
    stop_loss: 0.015
    take_profit: 0.03
  risk_reward_ratios:
  - *id073
  stock_name: Bounce
  timeframe:
  - 1min
- best_risk_reward: &id074
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 70
    oversold_threshold: 30
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:16:53'
  market_regime: neutral
  name: Bollinger_Bounce_Base_Bounce_1min
  performance_summary:
    max_drawdown: 0.0
    sharpe_ratio: 0.0
    total_trades: 0
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 50
  risk_management:
    stop_loss: 0.02
    take_profit: 0.04
  risk_reward_ratios:
  - *id074
  stock_name: Bounce
  timeframe:
  - 1min
- best_risk_reward: &id075
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 65
    oversold_threshold: 35
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:16:53'
  market_regime: neutral
  name: Bollinger_Bounce_Base_Bounce_1min
  performance_summary:
    max_drawdown: 0.0
    sharpe_ratio: 0.0
    total_trades: 0
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 50
  risk_management:
    stop_loss: 0.025
    take_profit: 0.05
  risk_reward_ratios:
  - *id075
  stock_name: Bounce
  timeframe:
  - 1min
- best_risk_reward: &id076
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 75
    oversold_threshold: 25
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:16:53'
  market_regime: neutral
  name: Bollinger_Bounce_Base_Bounce_1min
  performance_summary:
    max_drawdown: 0.0
    sharpe_ratio: 0.0
    total_trades: 0
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 50
  risk_management:
    stop_loss: 0.015
    take_profit: 0.03
  risk_reward_ratios:
  - *id076
  stock_name: Bounce
  timeframe:
  - 1min
- best_risk_reward: &id077
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 70
    oversold_threshold: 30
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:16:53'
  market_regime: neutral
  name: Bollinger_Bounce_Base_Bounce_1min
  performance_summary:
    max_drawdown: 0.0
    sharpe_ratio: 0.0
    total_trades: 0
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 50
  risk_management:
    stop_loss: 0.02
    take_profit: 0.04
  risk_reward_ratios:
  - *id077
  stock_name: Bounce
  timeframe:
  - 1min
- best_risk_reward: &id078
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 65
    oversold_threshold: 35
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:16:53'
  market_regime: neutral
  name: Bollinger_Bounce_Base_Bounce_1min
  performance_summary:
    max_drawdown: 0.0
    sharpe_ratio: 0.0
    total_trades: 0
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 50
  risk_management:
    stop_loss: 0.025
    take_profit: 0.05
  risk_reward_ratios:
  - *id078
  stock_name: Bounce
  timeframe:
  - 1min
- best_risk_reward: &id079
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 75
    oversold_threshold: 25
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:16:53'
  market_regime: neutral
  name: Bollinger_Bounce_Base_Bounce_1min
  performance_summary:
    max_drawdown: 0.0
    sharpe_ratio: 0.0
    total_trades: 0
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 50
  risk_management:
    stop_loss: 0.015
    take_profit: 0.03
  risk_reward_ratios:
  - *id079
  stock_name: Bounce
  timeframe:
  - 1min
- best_risk_reward: &id080
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 70
    oversold_threshold: 30
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:16:53'
  market_regime: neutral
  name: Bollinger_Bounce_Base_Bounce_1min
  performance_summary:
    max_drawdown: 0.0
    sharpe_ratio: 0.0
    total_trades: 0
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 50
  risk_management:
    stop_loss: 0.02
    take_profit: 0.04
  risk_reward_ratios:
  - *id080
  stock_name: Bounce
  timeframe:
  - 1min
- best_risk_reward: &id081
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 75
    oversold_threshold: 25
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:17:24'
  market_regime: neutral
  name: Bollinger_Bounce_360ONE_1min_Bounce_1min
  performance_summary:
    max_drawdown: 0.0
    sharpe_ratio: 0.0
    total_trades: 0
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 50
  risk_management:
    stop_loss: 0.015
    take_profit: 0.03
  risk_reward_ratios:
  - *id081
  stock_name: Bounce
  timeframe:
  - 1min
- best_risk_reward: &id082
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 70
    oversold_threshold: 30
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:17:24'
  market_regime: neutral
  name: Bollinger_Bounce_360ONE_1min_Bounce_1min
  performance_summary:
    max_drawdown: 0.0
    sharpe_ratio: 0.0
    total_trades: 0
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 50
  risk_management:
    stop_loss: 0.02
    take_profit: 0.04
  risk_reward_ratios:
  - *id082
  stock_name: Bounce
  timeframe:
  - 1min
- best_risk_reward: &id083
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 65
    oversold_threshold: 35
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:17:24'
  market_regime: neutral
  name: Bollinger_Bounce_360ONE_1min_Bounce_1min
  performance_summary:
    max_drawdown: 0.0
    sharpe_ratio: 0.0
    total_trades: 0
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 50
  risk_management:
    stop_loss: 0.025
    take_profit: 0.05
  risk_reward_ratios:
  - *id083
  stock_name: Bounce
  timeframe:
  - 1min
- best_risk_reward: &id084
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 75
    oversold_threshold: 25
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:17:24'
  market_regime: neutral
  name: Bollinger_Bounce_360ONE_1min_Bounce_1min
  performance_summary:
    max_drawdown: 0.0
    sharpe_ratio: 0.0
    total_trades: 0
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 50
  risk_management:
    stop_loss: 0.015
    take_profit: 0.03
  risk_reward_ratios:
  - *id084
  stock_name: Bounce
  timeframe:
  - 1min
- best_risk_reward: &id085
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 70
    oversold_threshold: 30
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:17:24'
  market_regime: neutral
  name: Bollinger_Bounce_360ONE_1min_Bounce_1min
  performance_summary:
    max_drawdown: 0.0
    sharpe_ratio: 0.0
    total_trades: 0
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 50
  risk_management:
    stop_loss: 0.02
    take_profit: 0.04
  risk_reward_ratios:
  - *id085
  stock_name: Bounce
  timeframe:
  - 1min
- best_risk_reward: &id086
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 65
    oversold_threshold: 35
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:17:24'
  market_regime: neutral
  name: Bollinger_Bounce_360ONE_1min_Bounce_1min
  performance_summary:
    max_drawdown: 0.0
    sharpe_ratio: 0.0
    total_trades: 0
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 50
  risk_management:
    stop_loss: 0.025
    take_profit: 0.05
  risk_reward_ratios:
  - *id086
  stock_name: Bounce
  timeframe:
  - 1min
- best_risk_reward: &id087
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 75
    oversold_threshold: 25
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:17:24'
  market_regime: neutral
  name: Bollinger_Bounce_360ONE_1min_Bounce_1min
  performance_summary:
    max_drawdown: 0.0
    sharpe_ratio: 0.0
    total_trades: 1
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 50
  risk_management:
    stop_loss: 0.015
    take_profit: 0.03
  risk_reward_ratios:
  - *id087
  stock_name: Bounce
  timeframe:
  - 1min
- best_risk_reward: &id088
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 70
    oversold_threshold: 30
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:17:24'
  market_regime: neutral
  name: Bollinger_Bounce_360ONE_1min_Bounce_1min
  performance_summary:
    max_drawdown: 0.0
    sharpe_ratio: 0.0
    total_trades: 1
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 50
  risk_management:
    stop_loss: 0.02
    take_profit: 0.04
  risk_reward_ratios:
  - *id088
  stock_name: Bounce
  timeframe:
  - 1min
- best_risk_reward: &id089
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 75
    oversold_threshold: 25
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:17:44'
  market_regime: neutral
  name: VWAP_Breakout_Base_Breakout_1min
  performance_summary:
    max_drawdown: 0.0
    sharpe_ratio: 0.0
    total_trades: 0
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 50
  risk_management:
    stop_loss: 0.015
    take_profit: 0.03
  risk_reward_ratios:
  - *id089
  stock_name: Breakout
  timeframe:
  - 1min
- best_risk_reward: &id090
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 70
    oversold_threshold: 30
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:17:44'
  market_regime: neutral
  name: VWAP_Breakout_Base_Breakout_1min
  performance_summary:
    max_drawdown: 0.0
    sharpe_ratio: 0.0
    total_trades: 0
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 50
  risk_management:
    stop_loss: 0.02
    take_profit: 0.04
  risk_reward_ratios:
  - *id090
  stock_name: Breakout
  timeframe:
  - 1min
- best_risk_reward: &id091
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 65
    oversold_threshold: 35
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:17:44'
  market_regime: neutral
  name: VWAP_Breakout_Base_Breakout_1min
  performance_summary:
    max_drawdown: 0.0
    sharpe_ratio: 0.0
    total_trades: 0
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 50
  risk_management:
    stop_loss: 0.025
    take_profit: 0.05
  risk_reward_ratios:
  - *id091
  stock_name: Breakout
  timeframe:
  - 1min
- best_risk_reward: &id092
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 75
    oversold_threshold: 25
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:17:44'
  market_regime: neutral
  name: VWAP_Breakout_Base_Breakout_1min
  performance_summary:
    max_drawdown: 0.0
    sharpe_ratio: 0.0
    total_trades: 0
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 50
  risk_management:
    stop_loss: 0.015
    take_profit: 0.03
  risk_reward_ratios:
  - *id092
  stock_name: Breakout
  timeframe:
  - 1min
- best_risk_reward: &id093
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 70
    oversold_threshold: 30
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:17:44'
  market_regime: neutral
  name: VWAP_Breakout_Base_Breakout_1min
  performance_summary:
    max_drawdown: 0.0
    sharpe_ratio: 0.0
    total_trades: 0
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 50
  risk_management:
    stop_loss: 0.02
    take_profit: 0.04
  risk_reward_ratios:
  - *id093
  stock_name: Breakout
  timeframe:
  - 1min
- best_risk_reward: &id094
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 65
    oversold_threshold: 35
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:17:44'
  market_regime: neutral
  name: VWAP_Breakout_Base_Breakout_1min
  performance_summary:
    max_drawdown: 0.0
    sharpe_ratio: 0.0
    total_trades: 0
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 50
  risk_management:
    stop_loss: 0.025
    take_profit: 0.05
  risk_reward_ratios:
  - *id094
  stock_name: Breakout
  timeframe:
  - 1min
- best_risk_reward: &id095
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 75
    oversold_threshold: 25
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:17:44'
  market_regime: neutral
  name: VWAP_Breakout_Base_Breakout_1min
  performance_summary:
    max_drawdown: 0.0
    sharpe_ratio: 0.0
    total_trades: 10
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 50
  risk_management:
    stop_loss: 0.015
    take_profit: 0.03
  risk_reward_ratios:
  - *id095
  stock_name: Breakout
  timeframe:
  - 1min
- best_risk_reward: &id096
  - 1
  - 2
  confidence_score: 0.0
  entry:
    overbought_threshold: 70
    oversold_threshold: 30
  exit:
    long_exit: rsi_14 > 60
    short_exit: rsi_14 < 40
  intraday_rules: {}
  last_updated: '2025-08-25 09:17:44'
  market_regime: neutral
  name: VWAP_Breakout_Base_Breakout_1min
  performance_summary:
    max_drawdown: 0.0
    sharpe_ratio: 0.0
    total_trades: 10
    win_rate: 0.0
  position_sizing:
    risk_per_trade: 0.02
  ranking: 50
  risk_management:
    stop_loss: 0.02
    take_profit: 0.04
  risk_reward_ratios:
  - *id096
  stock_name: Breakout
  timeframe:
  - 1min
