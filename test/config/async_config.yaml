# Async/Concurrent Processing Configuration
# Optimize these settings based on your hardware

# GPU Configuration
gpu:
  # Number of strategies to process concurrently
  # Increase for better GPU utilization, decrease if running out of VRAM
  concurrent_strategies: 6
  
  # Number of CUDA streams for parallel GPU operations
  # Should match or be slightly less than concurrent_strategies
  cuda_streams: 6
  
  # GPU memory management
  memory_pool_size: "7.5GB"  # Adjust based on available VRAM
  enable_memory_pool: true
  
  # Chunk size for processing (rows per chunk)
  # Larger = more memory usage, potentially faster
  # Smaller = less memory usage, more overhead
  chunk_size: 150000

# Async Processing Configuration
async:
  # Maximum number of concurrent workers for ThreadPoolExecutor
  # Should be 2-4x your CPU core count for I/O bound tasks
  max_workers: 8
  
  # Batch size for strategy processing
  # Process this many strategies concurrently before moving to next batch
  strategy_batch_size: 3
  
  # Delay between batches (seconds) to allow GPU memory cleanup
  batch_delay: 0.1
  
  # Timeout for individual strategy processing (seconds)
  strategy_timeout: 300

# Dask Configuration (if available)
dask:
  # Enable Dask-cuDF for distributed processing
  enable_dask: true
  
  # Number of partitions for Dask DataFrames
  # Should be equal to or multiple of concurrent_strategies
  npartitions: 10
  
  # Dask scheduler configuration
  scheduler: "threads"  # Options: "threads", "processes", "distributed"
  
  # Memory limits per worker
  memory_limit: "7GB"

# Performance Monitoring
monitoring:
  # Log GPU utilization every N strategies
  gpu_log_interval: 10
  
  # Log memory usage every N strategies  
  memory_log_interval: 5
  
  # Enable detailed timing logs
  enable_timing_logs: true
  
  # Save performance metrics to file
  save_metrics: true
  metrics_file: "data/performance_metrics.csv"

# Hardware-specific optimizations
hardware:
  # Auto-detect optimal settings based on hardware
  auto_detect: true
  
  # Manual overrides (only used if auto_detect is false)
  manual:
    gpu_memory_gb: 8
    cpu_cores: 12
    system_ram_gb: 24
    
    # Calculated optimal settings based on hardware
    optimal_concurrent_strategies: 8
    optimal_max_workers: 16
    optimal_chunk_size: 300000

# Fallback Configuration
fallback:
  # Settings to use if GPU is not available
  cpu_only:
    max_workers: 6
    chunk_size: 50000
    concurrent_strategies: 2
  
  # Settings for low-memory systems
  low_memory:
    max_workers: 2
    chunk_size: 25000
    concurrent_strategies: 1
    
# Advanced Settings
advanced:
  # Enable experimental features
  enable_experimental: false
  
  # Use memory mapping for large datasets
  use_memory_mapping: true
  
  # Enable JIT compilation for CuPy kernels
  enable_jit: true
  
  # Prefetch data for next symbol while processing current
  enable_prefetch: true
