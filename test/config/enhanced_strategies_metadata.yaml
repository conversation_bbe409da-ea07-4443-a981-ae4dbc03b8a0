# ═══════════════════════════════════════════════════════════════════════════════
# 🎯 ENHANCED STRATEGY METADATA - COMPREHENSIVE STRATEGY DEFINITIONS
# ═══════════════════════════════════════════════════════════════════════════════
# Advanced strategy metadata with regime-aware, event-driven, and time-based features

strategies:
  # ═══════════════════════════════════════════════════════════════════════════════
  # 🌅 MORNING BREAKOUT STRATEGIES
  # ═══════════════════════════════════════════════════════════════════════════════
  
  - id: "strat_001"
    name: "Morning Breakout IV Expansion"
    description: "Captures early breakout with low IV and increasing momentum in BANKNIFTY"
    enabled: true
    
    # Asset and timeframe configuration
    applicable_assets: ["BANKNIFTY"]
    timeframes: ["5min", "15min"]
    
    # Time and event configuration
    trigger_window: "09:15-10:15"
    expiry_types: ["weekly", "monthly"]
    event_types: []
    
    # Regime configuration
    market_regime: ["bullish", "volatile"]
    volatility_regime: ["low_IV"]
    
    # Signal logic
    signal_logic:
      long_conditions:
        - "close > high.shift(1)"
        - "volume > volume.rolling(20).mean() * 2"
        - "rsi_14 > 50"
        - "rsi_14 < 70"
        - "close > vwap"
      short_conditions:
        - "close < low.shift(1)"
        - "volume > volume.rolling(20).mean() * 2"
        - "rsi_14 < 50"
        - "rsi_14 > 30"
        - "close < vwap"
    
    # Confidence scoring
    confidence_scoring:
      type: "ensemble"
      metrics: ["past_accuracy", "sharpe_ratio", "drawdown"]
      min_threshold: 0.7
    
    min_confidence_threshold: 0.7
    
    # Risk management
    risk_model:
      max_capital_risk_percent: 2
      expected_risk_reward: "1:2.5"
      max_drawdown_percent: 5
      stop_loss_trigger: "1.2 * atr_14"
    
    max_capital_risk_percent: 2.0
    expected_risk_reward: "1:2.5"
    max_drawdown_percent: 5.0
    
    # Strategy classification
    strategy_type: ["breakout", "iv_expansion"]
    confidence_type: "backtested"
    holding_period: "15min-60min"
    
    # Performance tracking
    backtest:
      last_tested: "2024-06-20"
      roi: 18.4
      win_rate: 62.5
      sharpe: 1.83
      avg_holding_time: "22min"
      max_drawdown: 4.2
      profit_factor: 1.8
    
    live_performance:
      total_signals: 0
      successful_signals: 0
      win_rate: 0.0
      avg_return: 0.0
    
    # AI and evolution tags
    ai_tags: ["early_entry", "favorable_rr", "scalping", "low_iv_entry"]
    evolution: null
    
    # Timestamps
    created_at: "2024-07-18T10:00:00"
    last_updated: "2024-07-18T10:00:00"
    last_tested: "2024-06-20T00:00:00"

  # ═══════════════════════════════════════════════════════════════════════════════
  # 📊 MEAN REVERSION STRATEGIES
  # ═══════════════════════════════════════════════════════════════════════════════
  
  - id: "strat_002"
    name: "VWAP Mean Reversion"
    description: "Mean reversion strategy around VWAP with volume confirmation"
    enabled: true
    
    applicable_assets: ["NIFTY", "BANKNIFTY"]
    timeframes: ["5min", "15min", "30min"]
    
    trigger_window: "10:15-14:30"
    expiry_types: []
    event_types: []
    
    market_regime: ["sideways", "range_bound"]
    volatility_regime: ["normal_IV", "low_IV"]
    
    signal_logic:
      long_conditions:
        - "close < vwap * 0.998"
        - "rsi_14 < 35"
        - "volume > volume.rolling(20).mean() * 1.2"
        - "close > low.rolling(5).min()"
      short_conditions:
        - "close > vwap * 1.002"
        - "rsi_14 > 65"
        - "volume > volume.rolling(20).mean() * 1.2"
        - "close < high.rolling(5).max()"
    
    confidence_scoring:
      type: "backtested"
      metrics: ["win_rate", "profit_factor"]
      min_threshold: 0.65
    
    min_confidence_threshold: 0.65
    
    risk_model:
      max_capital_risk_percent: 1.5
      expected_risk_reward: "1:2"
      max_drawdown_percent: 3
      stop_loss_trigger: "0.8 * atr_14"
    
    max_capital_risk_percent: 1.5
    expected_risk_reward: "1:2"
    max_drawdown_percent: 3.0
    
    strategy_type: ["mean_reversion"]
    confidence_type: "backtested"
    holding_period: "30min-120min"
    
    backtest:
      last_tested: "2024-06-15"
      roi: 14.2
      win_rate: 68.3
      sharpe: 1.65
      avg_holding_time: "45min"
      max_drawdown: 2.8
      profit_factor: 2.1
    
    live_performance:
      total_signals: 0
      successful_signals: 0
      win_rate: 0.0
      avg_return: 0.0
    
    ai_tags: ["mean_reversion", "vwap_based", "volume_confirmation"]
    evolution: null
    
    created_at: "2024-07-18T10:00:00"
    last_updated: "2024-07-18T10:00:00"
    last_tested: "2024-06-15T00:00:00"

  # ═══════════════════════════════════════════════════════════════════════════════
  # 📈 GAP TRADING STRATEGIES
  # ═══════════════════════════════════════════════════════════════════════════════
  
  - id: "strat_003"
    name: "Gap Fill Strategy"
    description: "Trades gap fills with volume and momentum confirmation"
    enabled: true
    
    applicable_assets: ["NIFTY", "BANKNIFTY"]
    timeframes: ["5min", "15min"]
    
    trigger_window: "09:15-11:00"
    expiry_types: []
    event_types: ["gap_up", "gap_down"]
    
    market_regime: ["bullish", "bearish"]
    volatility_regime: ["normal_IV", "high_IV"]
    
    signal_logic:
      long_conditions:
        - "gap_down_detected"
        - "close > open"
        - "volume > volume.rolling(20).mean() * 1.5"
        - "rsi_14 > 40"
        - "close > low + (high - low) * 0.3"
      short_conditions:
        - "gap_up_detected"
        - "close < open"
        - "volume > volume.rolling(20).mean() * 1.5"
        - "rsi_14 < 60"
        - "close < high - (high - low) * 0.3"
    
    confidence_scoring:
      type: "ai_scored"
      metrics: ["gap_size", "volume_ratio", "momentum"]
      min_threshold: 0.75
    
    min_confidence_threshold: 0.75
    
    risk_model:
      max_capital_risk_percent: 2.5
      expected_risk_reward: "1:1.5"
      max_drawdown_percent: 4
      stop_loss_trigger: "gap_size * 0.5"
    
    max_capital_risk_percent: 2.5
    expected_risk_reward: "1:1.5"
    max_drawdown_percent: 4.0
    
    strategy_type: ["gap_fill"]
    confidence_type: "ai_scored"
    holding_period: "15min-45min"
    
    backtest:
      last_tested: "2024-06-10"
      roi: 22.1
      win_rate: 58.7
      sharpe: 1.92
      avg_holding_time: "28min"
      max_drawdown: 3.5
      profit_factor: 1.6
    
    live_performance:
      total_signals: 0
      successful_signals: 0
      win_rate: 0.0
      avg_return: 0.0
    
    ai_tags: ["gap_fill", "event_driven", "momentum"]
    evolution: null
    
    created_at: "2024-07-18T10:00:00"
    last_updated: "2024-07-18T10:00:00"
    last_tested: "2024-06-10T00:00:00"

  # ═══════════════════════════════════════════════════════════════════════════════
  # ⚡ POWER HOUR MOMENTUM STRATEGIES
  # ═══════════════════════════════════════════════════════════════════════════════
  
  - id: "strat_004"
    name: "Power Hour Momentum"
    description: "Captures momentum moves during the power hour (14:30-15:15)"
    enabled: true
    
    applicable_assets: ["NIFTY", "BANKNIFTY"]
    timeframes: ["5min", "15min"]
    
    trigger_window: "14:30-15:15"
    expiry_types: ["weekly"]
    event_types: []
    
    market_regime: ["bullish", "bearish", "volatile"]
    volatility_regime: ["normal_IV", "high_IV"]
    
    signal_logic:
      long_conditions:
        - "close > ema_20"
        - "ema_5 > ema_20"
        - "volume > volume.rolling(20).mean() * 1.8"
        - "rsi_14 > 55"
        - "macd > macd_signal"
      short_conditions:
        - "close < ema_20"
        - "ema_5 < ema_20"
        - "volume > volume.rolling(20).mean() * 1.8"
        - "rsi_14 < 45"
        - "macd < macd_signal"
    
    confidence_scoring:
      type: "ensemble"
      metrics: ["momentum_strength", "volume_confirmation", "trend_alignment"]
      min_threshold: 0.72
    
    min_confidence_threshold: 0.72
    
    risk_model:
      max_capital_risk_percent: 3
      expected_risk_reward: "1:2"
      max_drawdown_percent: 6
      stop_loss_trigger: "1.5 * atr_14"
    
    max_capital_risk_percent: 3.0
    expected_risk_reward: "1:2"
    max_drawdown_percent: 6.0
    
    strategy_type: ["momentum"]
    confidence_type: "ensemble"
    holding_period: "10min-30min"
    
    backtest:
      last_tested: "2024-06-25"
      roi: 16.8
      win_rate: 64.2
      sharpe: 1.74
      avg_holding_time: "18min"
      max_drawdown: 5.1
      profit_factor: 1.9
    
    live_performance:
      total_signals: 0
      successful_signals: 0
      win_rate: 0.0
      avg_return: 0.0
    
    ai_tags: ["power_hour", "momentum", "short_term", "high_frequency"]
    evolution: null
    
    created_at: "2024-07-18T10:00:00"
    last_updated: "2024-07-18T10:00:00"
    last_tested: "2024-06-25T00:00:00"
