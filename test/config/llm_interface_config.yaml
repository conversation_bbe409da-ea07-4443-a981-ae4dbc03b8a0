# ✅ LLM Interface Agent Configuration
# Ollama model selection and routing configuration for different query types

llm_interface:
  # ═══════════════════════════════════════════════════════════════════════════════
  # 🧠 MODEL SELECTION CONFIGURATION
  # ═══════════════════════════════════════════════════════════════════════════════
  
  models:
    # Primary models for different tasks based on capabilities research
    general_reasoning:
      model: "qwen3-8b"
      description: "Excellent multilingual LLM for general reasoning, natural language, config edits"
      use_cases: ["natural_language_query", "config_editing", "general_chat", "strategy_explanation"]
      temperature: 0.7
      max_tokens: 2048
      
    code_generation:
      model: "deepseek-coder-6.7b"
      description: "Highly capable coding assistant for Python strategy generation, error fixing"
      use_cases: ["code_generation", "bug_fixing", "strategy_creation", "python_development"]
      temperature: 0.3
      max_tokens: 4096
      
    code_explanation:
      model: "codellama-7b-instruct"
      description: "Detailed code explanations, generation, refactoring for risky logic rewrites"
      use_cases: ["code_explanation", "refactoring", "complex_logic", "documentation"]
      temperature: 0.4
      max_tokens: 3072
      
    instruction_following:
      model: "mistral-7b-instruct"
      description: "Fast, accurate instruction following for code + config fixes, error explanations"
      use_cases: ["error_fixing", "config_fixes", "instruction_following", "debugging"]
      temperature: 0.2
      max_tokens: 2048
      
    fast_chat:
      model: "phi4-mini"
      description: "Lightweight, great for quick Q&A, summaries, explanations"
      use_cases: ["quick_chat", "summaries", "fast_responses", "simple_queries"]
      temperature: 0.6
      max_tokens: 1024

  # ═══════════════════════════════════════════════════════════════════════════════
  # 🔀 QUERY ROUTING CONFIGURATION
  # ═══════════════════════════════════════════════════════════════════════════════
  
  routing:
    # Query classification patterns
    patterns:
      backtest_queries:
        keywords: ["backtest", "roi", "performance", "strategy", "profit", "loss", "sharpe", "drawdown"]
        agent: "performance_analysis_agent"
        model: "general_reasoning"
        
      code_queries:
        keywords: ["fix", "error", "bug", "code", "python", "generate", "create", "modify"]
        agent: "code_generation"
        model: "code_generation"
        
      trading_queries:
        keywords: ["trade", "signal", "buy", "sell", "position", "margin", "order", "execution"]
        agent: "execution_agent"
        model: "instruction_following"
        
      risk_queries:
        keywords: ["risk", "capital", "allocation", "drawdown", "stop loss", "position size"]
        agent: "risk_agent"
        model: "general_reasoning"
        
      market_queries:
        keywords: ["market", "price", "volume", "indicator", "regime", "trend", "volatility"]
        agent: "market_monitoring_agent"
        model: "general_reasoning"
        
      config_queries:
        keywords: ["config", "yaml", "strategy", "parameter", "setting", "tune", "optimize"]
        agent: "config_management"
        model: "general_reasoning"
        
      explanation_queries:
        keywords: ["why", "how", "explain", "what", "understand", "meaning", "reason"]
        agent: "explanation_system"
        model: "code_explanation"
        
      quick_queries:
        keywords: ["status", "summary", "quick", "brief", "overview", "list"]
        agent: "quick_response"
        model: "fast_chat"

  # ═══════════════════════════════════════════════════════════════════════════════
  # 🔌 AGENT INTEGRATION CONFIGURATION
  # ═══════════════════════════════════════════════════════════════════════════════
  
  agents:
    performance_analysis_agent:
      module: "agents.performance_analysis_agent"
      class: "PerformanceAnalysisAgent"
      config_path: "config/performance_analysis_config.yaml"
      methods:
        query_performance: "get_strategy_performance"
        get_metrics: "get_performance_metrics"
        generate_report: "generate_performance_report"
        
    market_monitoring_agent:
      module: "agents.market_monitoring_agent"
      class: "MarketMonitoringAgent"
      config_path: "config/market_monitoring_config.yaml"
      methods:
        get_regime: "get_market_regime"
        get_signals: "get_active_signals"
        get_indicators: "get_indicators"
        
    signal_generation_agent:
      module: "agents.signal_generation_agent"
      class: "SignalGenerationAgent"
      config_path: "config/signal_generation_config.yaml"
      methods:
        generate_signal: "process_market_data"
        get_strategies: "get_active_strategies"
        
    risk_agent:
      module: "agents.risk_agent"
      class: "RiskManagementAgent"
      config_path: "config/risk_management_config.yaml"
      methods:
        validate_trade: "validate_trade_request"
        get_portfolio: "get_portfolio_metrics"
        check_limits: "check_risk_limits"
        
    execution_agent:
      module: "agents.execution_agent"
      class: "ExecutionAgent"
      config_path: "config/execution_config.yaml"
      methods:
        place_order: "process_signal"
        get_trades: "get_execution_summary"
        modify_order: "modify_order"

  # ═══════════════════════════════════════════════════════════════════════════════
  # 🛠️ SYSTEM CONFIGURATION
  # ═══════════════════════════════════════════════════════════════════════════════
  
  system:
    ollama:
      base_url: "http://localhost:11434"
      timeout: 30
      retry_attempts: 3
      retry_delay: 1
      
    langchain:
      verbose: false
      streaming: true
      callbacks: ["console", "file"]
      
    langgraph:
      max_iterations: 10
      recursion_limit: 50
      
    logging:
      level: "INFO"
      file: "logs/llm_interface.log"
      format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
      
    performance:
      cache_responses: true
      cache_ttl: 300  # 5 minutes
      max_concurrent_requests: 5
      request_timeout: 60

  # ═══════════════════════════════════════════════════════════════════════════════
  # 📊 ANGEL ONE API INTEGRATION
  # ═══════════════════════════════════════════════════════════════════════════════
  
  angel_one:
    enabled: true
    queries:
      portfolio_status:
        keywords: ["portfolio", "holdings", "positions", "pnl", "profit", "loss"]
        endpoints: ["portfolio", "holdings", "positions"]
        
      margin_status:
        keywords: ["margin", "funds", "available", "used", "balance"]
        endpoints: ["funds", "margins"]
        
      trade_status:
        keywords: ["trades", "orders", "executed", "pending", "cancelled"]
        endpoints: ["orderbook", "tradebook"]
        
      market_data:
        keywords: ["price", "ltp", "volume", "market", "quote"]
        endpoints: ["quote", "ltp"]

  # ═══════════════════════════════════════════════════════════════════════════════
  # 🎯 RESPONSE TEMPLATES
  # ═══════════════════════════════════════════════════════════════════════════════
  
  templates:
    error_response: |
      ❌ **Error Processing Request**
      
      **Issue**: {error_message}
      **Suggestion**: {suggestion}
      **Model Used**: {model_name}
      
      Please try rephrasing your query or contact support if the issue persists.
      
    success_response: |
      ✅ **Query Processed Successfully**
      
      **Result**: {result}
      **Agent**: {agent_name}
      **Model**: {model_name}
      **Processing Time**: {processing_time}ms
      
    code_response: |
      🔧 **Code Generated/Fixed**
      
      ```python
      {code}
      ```
      
      **Explanation**: {explanation}
      **Model**: {model_name}
      **Confidence**: {confidence}%
