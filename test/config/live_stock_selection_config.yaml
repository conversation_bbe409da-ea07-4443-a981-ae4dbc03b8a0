# ═══════════════════════════════════════════════════════════════════════════════
# 🎯 LIVE STOCK SELECTION AND STRATEGY ASSIGNMENT CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
# Configuration for ML-driven premarket stock selection and strategy assignment
# Integrates with existing ML workflow for live trading applications

# ═══════════════════════════════════════════════════════════════════════════════
# 📊 DATA MANAGEMENT CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
data_management:
  # Historical data settings
  historical_days: 25  # Days of historical data to download
  data_directory: "data/live"  # Directory to save live data
  file_format: "parquet"  # Data storage format
  compression: "brotli"  # Compression for efficiency
  
  # Data sources and API settings
  api_rate_limit:
    requests_per_minute: 100
    requests_per_second: 2
    retry_attempts: 3
    retry_delay: 1.0  # seconds
    
  # Data quality validation
  data_quality:
    min_data_points: 20  # Minimum required data points
    max_missing_percentage: 10  # Maximum allowed missing data %
    outlier_detection: true
    outlier_method: "iqr"  # "iqr", "zscore", "isolation_forest"
    outlier_threshold: 3.0
    
  # Stock universe settings
  stock_universe:
    source_file: "data/lists/fno_list.csv"  # F&O stock list
    max_stocks_to_process: 500  # Maximum stocks to analyze
    exclude_symbols: []  # Symbols to exclude
    include_only: []  # If specified, only process these symbols

# ═══════════════════════════════════════════════════════════════════════════════
# 🔧 FEATURE ENGINEERING CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
feature_engineering:
  # Technical indicators to calculate
  technical_indicators:
    # Volatility indicators
    volatility:
      enabled: true
      rolling_window: 20
      methods: ["std", "atr", "garman_klass"]
      
    # RSI settings
    rsi:
      enabled: true
      periods: [14]
      overbought: 70
      oversold: 30
      
    # MACD settings
    macd:
      enabled: true
      fast_period: 12
      slow_period: 26
      signal_period: 9
      
    # Bollinger Bands
    bollinger_bands:
      enabled: true
      period: 20
      std_dev: 2.0
      calculate_percent_b: true
      
    # Exponential Moving Averages
    ema:
      enabled: true
      periods: [5, 20, 50]
      
    # Stochastic Oscillator
    stochastic:
      enabled: true
      k_period: 14
      d_period: 3
      smooth_k: 3
      
    # Volume indicators
    volume:
      enabled: true
      sma_period: 20
      volume_ratio_period: 10
      
    # Momentum indicators
    momentum:
      enabled: true
      roc_period: 10  # Rate of Change
      williams_r_period: 14  # Williams %R
      
  # Market condition features
  market_conditions:
    # Trend strength calculation
    trend_strength:
      enabled: true
      lookback_period: 20
      methods: ["adx", "linear_regression_slope"]
      
    # Volatility regime detection
    volatility_regime:
      enabled: true
      lookback_period: 20
      high_vol_threshold: 0.02  # 2%
      low_vol_threshold: 0.01   # 1%
      
    # Market correlation analysis
    market_correlation:
      enabled: true
      benchmark_symbol: "NIFTY"
      correlation_period: 20
      
  # Target variable settings
  target_variable:
    forward_return_days: 5  # 5-day forward return
    return_type: "percentage"  # "percentage" or "log"
    outlier_cap: 0.2  # Cap returns at ±20%
    
  # Feature preprocessing
  preprocessing:
    handle_missing: true
    missing_method: "forward_fill"  # "forward_fill", "interpolate", "drop"
    normalize_features: true
    normalization_method: "robust_scaler"  # "standard", "robust_scaler", "minmax"

# ═══════════════════════════════════════════════════════════════════════════════
# 🧠 ML PREDICTION CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
ml_prediction:
  # Model loading settings
  model_loading:
    models_directory: "data/models/enhanced"
    model_version: "latest"  # "latest" or specific timestamp
    fallback_enabled: true
    fallback_models: ["lightgbm"]  # Fallback if primary models fail
    
  # Prediction tasks (must match trained models)
  prediction_tasks:
    expected_return:
      enabled: true
      model_type: "regression"
      confidence_threshold: 0.6
      
    risk_metrics:
      enabled: true
      targets: ["expected_drawdown", "volatility"]
      model_type: "regression"
      
    strategy_suitability:
      enabled: true
      model_type: "classification"
      strategy_types: ["momentum", "mean_reversion", "breakout", "trend_following"]
      
  # Confidence interval settings
  confidence_intervals:
    enabled: true
    confidence_level: 0.95
    method: "bootstrap"  # "bootstrap", "quantile_regression"
    bootstrap_samples: 1000
    
  # Model versioning and fallback
  model_management:
    version_check: true
    performance_threshold: 0.6  # Minimum model performance
    fallback_strategy: "simple_scoring"  # Fallback if ML fails
    cache_predictions: true
    cache_duration: 300  # seconds

# ═══════════════════════════════════════════════════════════════════════════════
# 📈 STOCK SELECTION CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
stock_selection:
  # Scoring weights (must sum to 100%)
  scoring_weights:
    predicted_returns: 40  # 40% weight
    risk_adjusted_returns: 30  # 30% weight (Sharpe-like ratio)
    strategy_fit_confidence: 20  # 20% weight
    data_quality_score: 10  # 10% weight
    
  # Selection criteria
  selection_criteria:
    top_n_stocks: 50  # Select top 50 stocks
    min_score_threshold: 0.3  # Minimum composite score (reduced from 0.5)
    max_correlation: 0.8  # Maximum correlation between selected stocks
    
  # Diversification settings
  diversification:
    enabled: true
    sector_max_allocation: 0.3  # Maximum 30% from one sector
    market_cap_distribution:
      large_cap_min: 0.4  # Minimum 40% large cap
      mid_cap_max: 0.4   # Maximum 40% mid cap
      small_cap_max: 0.2  # Maximum 20% small cap
      
  # Risk management filters
  risk_filters:
    max_volatility: 0.05  # Maximum 5% daily volatility
    min_liquidity: 1000000  # Minimum daily volume
    max_drawdown_threshold: 0.15  # Maximum 15% expected drawdown
    
  # Quality filters
  quality_filters:
    min_data_completeness: 0.7  # 70% data completeness required (reduced from 90%)
    exclude_penny_stocks: true
    min_price: 10.0  # Minimum stock price
    max_price: 10000.0  # Maximum stock price

# ═══════════════════════════════════════════════════════════════════════════════
# 🎯 STRATEGY ASSIGNMENT CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
strategy_assignment:
  # Available strategies (from existing backtesting system)
  available_strategies:
    momentum:
      name: "Momentum Strategy"
      market_conditions: ["trending_up", "trending_down"]
      min_confidence: 0.7
      
    mean_reversion:
      name: "Mean Reversion Strategy"
      market_conditions: ["ranging", "low_volatility"]
      min_confidence: 0.6
      
    breakout:
      name: "Breakout Strategy"
      market_conditions: ["high_volatility", "trending"]
      min_confidence: 0.8
      
    trend_following:
      name: "Trend Following Strategy"
      market_conditions: ["trending_up", "trending_down"]
      min_confidence: 0.7
      
  # Strategy selection logic
  selection_logic:
    primary_strategy_method: "highest_confidence"  # "highest_confidence", "best_fit"
    backup_strategy_required: true
    backup_strategy_method: "second_best"
    min_confidence_gap: 0.1  # Minimum gap between primary and backup
    
  # Performance expectations
  performance_expectations:
    calculate_expected_metrics: true
    metrics: ["expected_return", "expected_sharpe", "expected_drawdown"]
    confidence_intervals: true
    
  # Market condition matching
  market_condition_matching:
    enabled: true
    current_market_weight: 0.7  # 70% weight to current conditions
    historical_performance_weight: 0.3  # 30% weight to historical performance

# ═══════════════════════════════════════════════════════════════════════════════
# 📋 REPORTING CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
reporting:
  # Report generation settings
  generate_reports: true
  report_directory: "reports/live_trading"
  report_formats: ["yaml", "json", "csv"]

  # Report content
  include_sections:
    selected_stocks: true
    strategy_assignments: true
    risk_assessment: true
    data_quality_summary: true
    model_performance: true
    market_conditions: true

  # Risk warnings
  risk_warnings:
    enabled: true
    high_risk_threshold: 0.8  # Stocks with risk score > 0.8
    correlation_warning_threshold: 0.9  # Warn if correlation > 0.9
    concentration_warning_threshold: 0.4  # Warn if sector concentration > 40%

  # Recommendations
  recommendations:
    enabled: true
    include_alternative_strategies: true
    include_risk_mitigation: true
    include_monitoring_suggestions: true

# ═══════════════════════════════════════════════════════════════════════════════
# ⚙️ SYSTEM CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
system:
  # Performance settings
  performance:
    max_concurrent_downloads: 10  # Concurrent data downloads
    max_concurrent_calculations: 5  # Concurrent feature calculations
    chunk_size: 100  # Process stocks in chunks
    memory_limit_mb: 2048  # Memory limit in MB

  # Logging settings
  logging:
    level: "INFO"  # DEBUG, INFO, WARNING, ERROR
    log_file: "logs/live_stock_selection.log"
    max_log_size_mb: 100
    backup_count: 5

  # Error handling
  error_handling:
    continue_on_error: true
    max_errors_per_stock: 3
    error_notification: false

  # Caching settings
  caching:
    enabled: true
    cache_directory: "data/cache/live_selection"
    cache_duration_hours: 1  # Cache data for 1 hour

  # Integration settings
  integration:
    clean_trading_system: true
    event_bus_enabled: true
    websocket_updates: false  # For premarket, websocket not needed

# ═══════════════════════════════════════════════════════════════════════════════
# 🔄 WORKFLOW CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
workflow:
  # Execution order
  execution_steps:
    - "data_management"
    - "feature_engineering"
    - "ml_prediction"
    - "stock_selection"
    - "strategy_assignment"
    - "report_generation"

  # Timing settings
  timing:
    max_total_runtime_minutes: 30  # Maximum 30 minutes for entire workflow
    step_timeout_minutes: 10  # Maximum 10 minutes per step

  # Validation settings
  validation:
    validate_each_step: true
    min_stocks_selected: 10  # Minimum stocks to select
    max_stocks_selected: 100  # Maximum stocks to select

  # Fallback settings
  fallback:
    enabled: true
    fallback_stock_list: "config/nifty500_stocks.json"  # Fallback stock universe
    simple_selection_method: "momentum_rsi"  # Simple selection if ML fails
