# Production Risk Management Configuration
# =====================================
# 
# This configuration file contains all risk management parameters
# for production trading. Adjust these values carefully as they
# directly impact trading safety and profitability.

# Capital Management
capital_management:
  initial_balance: 100000  # Starting capital in INR
  max_daily_risk: 0.02     # 2% maximum daily portfolio risk
  max_position_risk: 0.005 # 0.5% maximum risk per position
  max_portfolio_heat: 0.10 # 10% maximum total portfolio risk
  emergency_cash_reserve: 0.20  # 20% emergency cash reserve

# Position Sizing
position_sizing:
  method: "adaptive"  # Options: fixed, kelly, volatility, adaptive
  min_position_size: 0.001  # 0.1% minimum position
  max_position_size: 0.05   # 5% maximum position
  kelly_lookback_days: 30
  volatility_lookback_days: 20
  kelly_safety_factor: 0.25  # Use 25% of Kelly recommendation
  volatility_target: 0.15    # 15% target volatility

# Risk Limits
risk_limits:
  max_drawdown: 0.10        # 10% maximum drawdown
  max_daily_loss: 0.03      # 3% maximum daily loss
  max_leverage: 2.0         # 2x maximum leverage
  max_positions: 10         # Maximum number of positions
  max_positions_per_symbol: 2  # Maximum positions per symbol
  max_sector_concentration: 0.30  # 30% maximum sector concentration
  max_symbol_concentration: 0.15  # 15% maximum symbol concentration

# Risk-Reward Requirements
risk_reward:
  min_risk_reward_ratio: 1.5    # Minimum 1.5:1 risk-reward
  preferred_risk_reward_ratio: 2.0  # Preferred 2:1 risk-reward
  max_stop_loss_distance: 0.05  # 5% maximum stop loss distance
  min_take_profit_distance: 0.03  # 3% minimum take profit distance

# Circuit Breakers
circuit_breakers:
  daily_loss:
    enabled: true
    threshold: 0.03  # 3% daily loss triggers circuit breaker
    cooldown_minutes: 60
    action: "stop_new_trades"
    
  drawdown:
    enabled: true
    threshold: 0.10  # 10% drawdown triggers circuit breaker
    cooldown_minutes: 120
    action: "stop_all_trading"
    
  leverage:
    enabled: true
    threshold: 2.0   # 2x leverage triggers circuit breaker
    cooldown_minutes: 30
    action: "reduce_positions"
    
  consecutive_losses:
    enabled: true
    threshold: 5     # 5 consecutive losses
    cooldown_minutes: 60
    action: "pause_strategy"
    
  api_failures:
    enabled: true
    threshold: 0.5   # 50% API failure rate
    cooldown_minutes: 15
    action: "switch_to_backup"
    
  volatility_spike:
    enabled: true
    threshold: 3.0   # 3x normal volatility
    cooldown_minutes: 30
    action: "reduce_position_sizes"

# Emergency Stops
emergency_stops:
  market_crash:
    enabled: true
    condition: "market_drop_5min"
    threshold: 0.05  # 5% market drop in 5 minutes
    action: "emergency_exit_all"
    
  flash_crash:
    enabled: true
    condition: "market_drop_1min"
    threshold: 0.03  # 3% market drop in 1 minute
    action: "emergency_exit_all"
    
  system_overload:
    enabled: true
    condition: "cpu_memory_usage"
    threshold: 0.95  # 95% system resource usage
    action: "pause_new_trades"
    
  data_feed_failure:
    enabled: true
    condition: "data_staleness"
    threshold: 300   # 5 minutes stale data
    action: "stop_all_trading"
    
  network_latency:
    enabled: true
    condition: "high_latency"
    threshold: 5000  # 5 seconds latency
    action: "pause_new_trades"

# Monitoring and Alerting
monitoring:
  risk_check_interval: 10      # seconds
  performance_check_interval: 60  # seconds
  health_check_interval: 30    # seconds
  
  alerts:
    email_enabled: true
    sms_enabled: true
    webhook_enabled: true
    
  thresholds:
    warning_drawdown: 0.05     # 5% drawdown warning
    critical_drawdown: 0.08    # 8% drawdown critical alert
    warning_daily_loss: 0.02   # 2% daily loss warning
    critical_daily_loss: 0.025 # 2.5% daily loss critical alert

# Recovery Settings
recovery:
  gradual_recovery_enabled: true
  recovery_position_size_factor: 0.5  # Start with 50% position sizes
  recovery_risk_factor: 0.5           # Use 50% normal risk
  recovery_period_days: 5             # 5 days recovery period
  
  recovery_thresholds:
    start_recovery_drawdown: 0.05     # Start recovery at 5% drawdown
    full_recovery_profit: 0.02        # Full recovery at 2% profit
    
# Strategy-Specific Risk
strategy_risk:
  momentum:
    max_positions: 5
    max_risk_per_trade: 0.004  # 0.4%
    preferred_timeframe: "15min"
    
  mean_reversion:
    max_positions: 3
    max_risk_per_trade: 0.003  # 0.3%
    preferred_timeframe: "5min"
    
  breakout:
    max_positions: 4
    max_risk_per_trade: 0.005  # 0.5%
    preferred_timeframe: "30min"

# Market Conditions
market_conditions:
  high_volatility:
    vix_threshold: 25
    position_size_factor: 0.7  # Reduce position sizes by 30%
    max_positions_factor: 0.8  # Reduce max positions by 20%
    
  low_volatility:
    vix_threshold: 15
    position_size_factor: 1.2  # Increase position sizes by 20%
    max_positions_factor: 1.0  # Keep max positions same
    
  trending_market:
    trend_strength_threshold: 0.7
    momentum_strategy_weight: 1.5
    mean_reversion_strategy_weight: 0.5
    
  ranging_market:
    trend_strength_threshold: 0.3
    momentum_strategy_weight: 0.5
    mean_reversion_strategy_weight: 1.5

# Compliance and Regulatory
compliance:
  max_intraday_turnover: 1000000  # 10 Lakh max intraday turnover
  position_reporting_threshold: 50000  # Report positions > 50K
  risk_reporting_enabled: true
  audit_trail_enabled: true
  
# Backup and Failover
backup:
  backup_broker_enabled: true
  backup_data_feed_enabled: true
  failover_timeout_seconds: 30
  backup_risk_limits_factor: 0.8  # Use 80% of normal limits on backup

# Development and Testing
development:
  paper_trading_mode: true
  risk_override_enabled: false  # NEVER enable in production
  debug_logging_enabled: true
  performance_logging_enabled: true
