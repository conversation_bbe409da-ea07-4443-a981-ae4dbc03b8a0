# ═══════════════════════════════════════════════════════════════════════════════
# 🚀 CONTINUOUS TRADING CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════

# Trading Session Settings
session:
  max_daily_trades: 5
  signal_check_interval_seconds: 30
  position_check_interval_seconds: 10
  max_concurrent_positions: 3
  
  # Trading Windows
  trading_window:
    start_time: "09:20"
    end_time: "14:30"
    auto_square_off_time: "15:20"
  
  # Stock Selection
  stock_selection:
    max_stocks_to_monitor: 20
    selection_method: "ai_driven"  # ai_driven, nifty_50, sector_rotation
    reselection_interval_hours: 4
    
    # Filters
    filters:
      min_market_cap: "Large"  # Large, Mid, Small
      min_liquidity_volume: 100000
      max_volatility_percentile: 85
      exclude_sectors: []

# AI Agent Coordination
agents:
  signal_generation:
    confidence_threshold: 0.65
    max_signals_per_stock_per_hour: 2
    signal_timeout_minutes: 15
    
    # Strategy Weights
    strategy_weights:
      momentum: 0.3
      mean_reversion: 0.2
      breakout: 0.25
      ml_ensemble: 0.25
  
  risk_management:
    max_position_size_percent: 10.0
    max_portfolio_risk_percent: 15.0
    correlation_limit: 0.7
    
    # Dynamic Risk Adjustment
    dynamic_risk:
      enable: true
      volatility_adjustment: true
      market_regime_adjustment: true
  
  execution:
    order_type: "LIMIT"  # LIMIT, MARKET
    slippage_tolerance_percent: 0.1
    max_order_retry_attempts: 3
    order_timeout_seconds: 30

# Position Management
position_management:
  # Entry Rules
  entry:
    min_risk_reward_ratio: 1.5
    max_entry_attempts: 2
    entry_price_tolerance_percent: 0.2
  
  # Exit Rules
  exit:
    trailing_stop_enable: true
    trailing_stop_percent: 1.0
    profit_booking_levels: [1.5, 2.0, 3.0]  # Risk-reward ratios
    max_holding_time_minutes: 240  # 4 hours max
  
  # Stop Loss Management
  stop_loss:
    initial_sl_percent: 1.5
    adaptive_sl_enable: true
    breakeven_move_percent: 1.0

# Market Monitoring
market_monitoring:
  # Data Sources
  data_sources:
    primary: "angel_one"
    backup: "yahoo_finance"
  
  # Timeframes
  timeframes: ["1min", "5min", "15min"]
  
  # Indicators
  indicators:
    trend: ["ema_20", "ema_50", "adx"]
    momentum: ["rsi", "macd", "stoch"]
    volume: ["volume_sma", "vwap"]
    volatility: ["atr", "bollinger_bands"]

# Performance Tracking
performance:
  # Metrics
  track_metrics:
    - "total_pnl"
    - "win_rate"
    - "profit_factor"
    - "max_drawdown"
    - "sharpe_ratio"
    - "trades_per_hour"
  
  # Reporting
  reporting:
    real_time_updates: true
    update_interval_seconds: 60
    save_interval_minutes: 15
    
  # Alerts
  alerts:
    max_drawdown_percent: 5.0
    low_win_rate_threshold: 40.0
    high_rejection_rate_threshold: 70.0

# Safety Features
safety:
  # Circuit Breakers
  circuit_breakers:
    max_consecutive_losses: 3
    max_daily_loss_percent: 3.0
    max_position_loss_percent: 2.0
  
  # Emergency Stops
  emergency_stops:
    market_crash_detection: true
    unusual_volatility_threshold: 3.0  # Standard deviations
    api_failure_max_retries: 5
  
  # Validation
  validation:
    pre_trade_checks: true
    post_trade_verification: true
    real_time_risk_monitoring: true

# Logging and Monitoring
logging:
  level: "INFO"
  file_rotation: true
  max_file_size_mb: 100
  backup_count: 10
  
  # Log Categories
  categories:
    trading_decisions: true
    risk_events: true
    execution_details: true
    performance_metrics: true
    system_health: true

# System Resources
system:
  # Performance
  max_cpu_usage_percent: 80
  max_memory_usage_mb: 2048
  
  # Networking
  api_timeout_seconds: 10
  websocket_reconnect_attempts: 5
  
  # Storage
  data_retention_days: 30
  report_retention_days: 90
