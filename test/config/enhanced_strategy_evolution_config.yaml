# ═══════════════════════════════════════════════════════════════════════════════
# 🧬 ENHANCED STRATEGY EVOLUTION AGENT CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
# Configuration for the Enhanced Strategy Evolution Agent that addresses all
# enhancement points from the error.txt file

# ═══════════════════════════════════════════════════════════════════════════════
# 🎯 EVOLUTION CONFIGURATION - PRODUCTION OPTIMIZED
# ═══════════════════════════════════════════════════════════════════════════════
evolution:
  # Population parameters - optimized for production
  population_size: 100                # Increased for better genetic diversity
  elite_size: 20                      # Increased to preserve more top performers
  max_generations: 500                # Increased for longer evolution cycles

  # Multi-objective optimization with production targets
  objectives:
    - name: "sharpe_ratio"
      weight: 0.35                     # Slightly reduced to balance with other metrics
      direction: "maximize"
      target_value: 2.0                # Higher target for production
    - name: "max_drawdown"
      weight: 0.25                     # Reduced weight but still important
      direction: "minimize"
      target_value: 8.0                # Stricter drawdown control
    - name: "win_rate"
      weight: 0.25                     # Balanced importance
      direction: "maximize"
      target_value: 0.65               # Higher win rate target
    - name: "roi"
      weight: 0.15                     # Added ROI as objective
      direction: "maximize"
      target_value: 15.0               # 15% ROI target

  # Strategy enhancement parameters - OPTIMIZED FOR THROUGHPUT
  max_variants_per_strategy: 8        # Reduced for efficiency (was 12)
  min_variants_per_strategy: 3        # Minimum variants to add (if any meet threshold)
  min_ranking_threshold: 25           # LOWERED threshold for more variants (was 50)
  quality_scaling: false              # Disabled for consistent output

  # Ranking system (as requested in memories)
  ranking_system:
    initial_ranking: 100              # Initial ranking for new strategies
    ranking_decay_rate: 0.95          # Decay rate for poor performance
    ranking_boost_rate: 1.05          # Boost rate for good performance
    min_ranking: 10                   # Minimum ranking before removal
    max_ranking: 100                  # Maximum ranking cap

  # Stock selection criteria - production ready
  stock_selection_criteria:
    min_volume: 50000                 # Higher volume requirement for liquidity
    min_price: 10.0                   # Higher minimum price to avoid penny stocks
    max_price: 50000.0                # Reasonable upper limit
    sectors: ["all"]                  # Process all sectors
    max_stocks_per_strategy: 500      # Increased for comprehensive testing

  # Backtesting integration - production scale
  backtesting_config:
    max_symbols: 500                  # Increased for production scale
    max_files: 3000                   # Increased file processing capacity
    ranking_threshold: 25             # Higher threshold for quality
    enable_caching: true              # Essential for performance
    cache_duration_hours: 48          # Longer cache duration for stability

# ═══════════════════════════════════════════════════════════════════════════════
# 🏪 STORAGE CONFIGURATION (Enhancement Point #7)
# ═══════════════════════════════════════════════════════════════════════════════
storage:
  # Database configuration (separate from YAML config)
  database_path: "data/evolved_strategies.db"
  backup_interval_hours: 24
  max_backup_files: 10
  
  # File storage paths
  strategies_dir: "data/evolved_strategies"
  performance_dir: "data/evolution_performance"
  backup_dir: "data/evolution_backups"
  
  # Data retention policies
  retention:
    performance_history_days: 365     # Keep performance history for 1 year
    failed_strategies_days: 30        # Keep failed strategies for 30 days
    backup_files_days: 90             # Keep backup files for 90 days

# ═══════════════════════════════════════════════════════════════════════════════
# 🌊 MARKET REGIME ADAPTATION (Enhancement Point #5)
# ═══════════════════════════════════════════════════════════════════════════════
market_regime:
  enable_adaptation: true
  
  # Regime detection parameters
  detection_window_days: 30           # Days to analyze for regime detection
  volatility_threshold: 0.02          # Volatility threshold for regime classification
  trend_threshold: 0.05               # Trend threshold for regime classification
  
  # Learned adaptations (instead of hardcoded)
  adaptation_learning:
    enable_learning: true
    learning_rate: 0.01
    adaptation_memory_days: 90        # Days to remember adaptations
    min_samples_for_learning: 100     # Minimum samples to learn adaptations
  
  # Regime-specific parameters
  regimes:
    bullish:
      risk_multiplier_range: [0.8, 1.2]
      stop_loss_adjustment_range: [0.9, 1.1]
      take_profit_adjustment_range: [1.0, 1.3]
    bearish:
      risk_multiplier_range: [0.6, 1.0]
      stop_loss_adjustment_range: [0.8, 1.0]
      take_profit_adjustment_range: [0.8, 1.2]
    sideways:
      risk_multiplier_range: [0.7, 1.1]
      stop_loss_adjustment_range: [0.9, 1.1]
      take_profit_adjustment_range: [0.9, 1.2]
    volatile:
      risk_multiplier_range: [0.5, 0.9]
      stop_loss_adjustment_range: [1.1, 1.4]
      take_profit_adjustment_range: [1.2, 1.8]

# ═══════════════════════════════════════════════════════════════════════════════
# 🔄 STRATEGY LIFECYCLE MANAGEMENT (Enhancement Point #9)
# ═══════════════════════════════════════════════════════════════════════════════
lifecycle:
  # Status thresholds (based on composite fitness score 0-1)
  status_thresholds:
    champion: 0.8                     # Promote to champion
    challenger: 0.6                   # Promote to challenger
    testing: 0.4                      # Keep in testing
    candidate: 0.2                    # Keep as candidate
    failed: 0.0                       # Mark as failed
  
  # Promotion/demotion rules
  promotion_rules:
    min_evaluation_period_days: 7     # Minimum days before promotion
    min_trades_for_promotion: 10      # Minimum trades before promotion
    consecutive_good_days: 3          # Consecutive good days for promotion
  
  demotion_rules:
    max_consecutive_losses: 5         # Max consecutive losses before demotion
    max_drawdown_for_demotion: 15.0   # Max drawdown % for demotion
    min_sharpe_for_champion: 1.0      # Min Sharpe ratio to remain champion
  
  # A/B testing configuration
  ab_testing:
    enable_ab_testing: true
    test_duration_days: 14            # Duration of A/B tests
    min_sample_size: 50               # Minimum sample size for statistical significance
    confidence_level: 0.95            # Statistical confidence level

# ═══════════════════════════════════════════════════════════════════════════════
# 📈 ENHANCED MONITORING (Enhancement Point #10)
# ═══════════════════════════════════════════════════════════════════════════════
monitoring:
  # Structured logging
  logging:
    level: "INFO"
    format: "json"                    # JSON format for structured logging
    file: "logs/enhanced_strategy_evolution.log"
    max_file_size_mb: 100
    backup_count: 5
  
  # Real-time monitoring
  real_time:
    enable_monitoring: true
    update_interval_seconds: 60       # Update interval for real-time metrics
    
    # Metrics to track
    metrics:
      - "generation_progress"
      - "fitness_scores"
      - "strategy_performance"
      - "agent_health"
      - "evolution_convergence"
  
  # Alerting
  alerts:
    enable_alerts: true
    
    # Alert conditions
    conditions:
      - metric: "avg_fitness_decline"
        threshold: 0.1
        duration_minutes: 30
      - metric: "failed_strategies_ratio"
        threshold: 0.5
        duration_minutes: 60
      - metric: "evolution_stagnation"
        threshold: 10                 # Generations without improvement
        duration_minutes: 120

# ═══════════════════════════════════════════════════════════════════════════════
# 🔧 OPTIMIZATION PARAMETERS - PRODUCTION OPTIMIZED
# ═══════════════════════════════════════════════════════════════════════════════
optimization:
  # Optuna configuration - production settings
  optuna:
    n_trials: 50                      # Increased trials for better optimization
    timeout_seconds: 7200             # 2 hours timeout for thorough optimization
    n_jobs: 8                         # Balanced parallel processing

    # Sampler configuration
    sampler: "TPE"                    # Tree-structured Parzen Estimator
    pruner: "MedianPruner"            # Median pruning for early stopping

  # Parameter ranges for optimization - refined for production
  parameter_ranges:
    risk_pct: [0.8, 2.5]             # Tighter risk range for production
    reward_pct: [1.5, 4.0]           # Balanced reward range
    stop_loss: [0.008, 0.025]        # Refined stop loss range
    take_profit: [0.015, 0.045]      # Added take profit optimization
    rsi_period: [12, 18]             # Narrowed RSI period for intraday
    ema_period: [8, 34]              # Fibonacci-based EMA periods
    volume_multiplier: [1.5, 2.5]    # Tighter volume multiplier range

  # Risk/reward ratios for production (as requested in memories)
  risk_reward_ratios:
    conservative: 1.5                 # 1:1.5 risk/reward
    balanced: 2.0                     # 1:2 risk/reward
    aggressive: 2.5                   # 1:2.5 risk/reward

# ═══════════════════════════════════════════════════════════════════════════════
# 🚀 PERFORMANCE OPTIMIZATION - PRODUCTION TUNED
# ═══════════════════════════════════════════════════════════════════════════════
performance:
  # Parallel processing - optimized for GPU acceleration
  parallel_processing:
    enable_multiprocessing: true
    max_workers: 16                   # Optimized for GPU parallel processing
    chunk_size: 50                    # Smaller chunks for better GPU utilization
    enable_gpu_batching: true         # Enable GPU batch processing
    gpu_batch_size: 8                 # Process 8 strategies per GPU batch
    concurrent_stocks: 16             # Process multiple stocks simultaneously

  # GPU acceleration settings (as requested in memories)
  gpu_acceleration:
    enable_gpu: true                  # Enable GPU acceleration
    preferred_library: "polars[gpu]"  # Use polars[gpu] as specified in memories
    gpu_memory_fraction: 0.75         # Reduced for better parallel processing
    fallback_to_cpu: true             # Fallback if GPU unavailable
    enable_async_gpu: true            # Enable asynchronous GPU operations
    gpu_streams: 4                    # Use multiple CUDA streams

    # Multi-layer parallel processing configuration
    max_concurrent_batches: 4         # Maximum concurrent batches to process
    max_batch_size: 24                # Optimal batch size for RTX 3060Ti
    variants_per_stock: 3             # Number of variants per stock
    gpu_recovery_delay: 0.1           # Minimal delay between batches (seconds)
    enable_adaptive_batching: true    # Enable adaptive batch sizing
    stream_isolation: true            # Use CUDA streams for batch isolation

    # Strategy-level parallel processing
    max_concurrent_strategies: 10      # Maximum concurrent strategies (conservative)
    enable_strategy_parallelism: true # Enable concurrent strategy processing
    gpu_worker_allocation: "balanced" # balanced, dedicated, or shared

  # Memory management - production optimized
  memory:
    max_memory_usage_gb: 32           # Increased for production workloads
    enable_garbage_collection: true  # Essential for long-running processes
    gc_interval_seconds: 180          # More frequent GC for stability
    memory_monitoring: true           # Monitor memory usage

  # Caching - enhanced for production
  caching:
    enable_result_caching: true
    cache_size_mb: 1024              # Increased cache size for better performance
    cache_ttl_hours: 72              # Longer TTL for production stability
    enable_persistent_cache: true    # Persist cache across restarts

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 DEVELOPMENT AND TESTING
# ═══════════════════════════════════════════════════════════════════════════════
development:
  # Testing mode
  enable_testing_mode: false
  test_data_path: "data/test/sample_data.parquet"
  
  # Debug settings
  debug:
    enable_debug_logging: false
    save_intermediate_results: false
    enable_profiling: false
    profiling_output_dir: "data/profiling"
  
  # Simulation settings
  simulation:
    enable_simulation: false
    simulation_speed_multiplier: 100   # Speed up simulation
    mock_backtesting: false          # Use mock backtesting for testing

# ═══════════════════════════════════════════════════════════════════════════════
# 🔗 INTEGRATION SETTINGS
# ═══════════════════════════════════════════════════════════════════════════════
integration:
  # Agent communication
  agents:
    signal_agent:
      enabled: true
      config_path: "config/strategies.yaml"
    backtesting_agent:
      enabled: true
      function_name: "run_backtesting_for_evolution"
  
  # External services
  external_services:
    enable_webhook_notifications: false
    webhook_url: ""
    
    enable_database_sync: false
    sync_interval_hours: 6
