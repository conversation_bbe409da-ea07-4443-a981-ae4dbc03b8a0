#!/usr/bin/env python3
"""
Lazy Imports and Utilities for Strategy Evolution Agent

This module contains lazy loading utilities and import functions
for heavy dependencies used by the Strategy Evolution Agent.
"""

import importlib
import logging
from datetime import datetime

# Configure logging with reduced verbosity
logger = logging.getLogger(__name__)
logger.setLevel(logging.ERROR)

# File handler for critical logs
file_handler = logging.FileHandler('logs/strategy_evolution.log')
file_handler.setLevel(logging.ERROR)
file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
logger.addHandler(file_handler)

# Stream handler for minimal terminal output (only critical errors)
stream_handler = logging.StreamHandler()
stream_handler.setLevel(logging.CRITICAL)
stream_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
logger.addHandler(stream_handler)

# Configure root logger to suppress excessive output
logging.basicConfig(level=logging.CRITICAL, handlers=[])

def log_critical(message: str):
    """Log critical system events with timestamp"""
    timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
    logger.critical(f"[{timestamp}] {message}")

# Lazy loading utility class
class LazyLoader:
    """Utility class for lazy loading of modules"""
    def __init__(self, module_name: str, attribute: str = None):
        self.module_name = module_name
        self.attribute = attribute
        self._module = None

    def __getattr__(self, name):
        if self._module is None:
            self._module = importlib.import_module(self.module_name)

        if self.attribute:
            attr = getattr(self._module, self.attribute)
            return getattr(attr, name)
        else:
            return getattr(self._module, name)

    def __call__(self, *args, **kwargs):
        if self._module is None:
            self._module = importlib.import_module(self.module_name)

        if self.attribute:
            return getattr(self._module, self.attribute)(*args, **kwargs)
        else:
            return self._module(*args, **kwargs)

# Lazy imports for heavy dependencies
np = LazyLoader('numpy')
pl = LazyLoader('polars')
pa = LazyLoader('pyarrow')
ThreadPoolExecutor = LazyLoader('concurrent.futures', 'ThreadPoolExecutor')

def lazy_import_optuna():
    """Lazy import for Optuna"""
    try:
        optuna = importlib.import_module('optuna')
        TPESampler = getattr(importlib.import_module('optuna.samplers'), 'TPESampler')
        MedianPruner = getattr(importlib.import_module('optuna.pruners'), 'MedianPruner')
        # Configure Optuna's logger to suppress messages
        optuna.logging.set_verbosity(optuna.logging.ERROR)
        return optuna, TPESampler, MedianPruner, True
    except ImportError:
        return None, None, None, False

def lazy_import_polars_talib():
    """Lazy import for polars-talib"""
    try:
        return importlib.import_module('polars_talib'), True
    except ImportError:
        return None, False

def lazy_import_mintalib():
    """Lazy import for mintalib"""
    try:
        return importlib.import_module('mintalib'), True
    except ImportError:
        return None, False

def lazy_import_gplearn():
    """Lazy import for gplearn"""
    try:
        genetic_module = importlib.import_module('gplearn.genetic')
        functions_module = importlib.import_module('gplearn.functions')
        return genetic_module.SymbolicRegressor, functions_module.make_function, True
    except ImportError:
        return None, None, False

def lazy_import_agents():
    """Lazy import for agent dependencies"""
    try:
        PerformanceAnalysisAgent = getattr(importlib.import_module('agents.performance_analysis_agent'), 'PerformanceAnalysisAgent')
        MarketMonitoringAgent = getattr(importlib.import_module('agents.market_monitoring_agent'), 'MarketMonitoringAgent')
        AITrainingAgent = getattr(importlib.import_module('agents.ai_training_agent'), 'AITrainingAgent')
        run_backtesting_for_evolution = getattr(importlib.import_module('agents.enhanced_backtesting_kimi'), 'run_backtesting_for_evolution')
        return PerformanceAnalysisAgent, MarketMonitoringAgent, AITrainingAgent, run_backtesting_for_evolution
    except ImportError as e:
        logger.error(f"Failed to import agent dependencies: {e}")
        return None, None, None, None
