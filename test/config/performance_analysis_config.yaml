# ═══════════════════════════════════════════════════════════════════════════════
# 📊 PERFOR<PERSON>NC<PERSON> ANALYSIS AGENT CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════

# ═══════════════════════════════════════════════════════════════════════════════
# 📡 ANGEL ONE API CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
angel_one_api:
  enabled: true
  
  # Connection settings
  connection:
    timeout_seconds: 30
    retry_attempts: 3
    retry_delay_seconds: 5
  
  # Data fetching settings
  data_fetching:
    tradebook_fetch_interval_minutes: 15
    position_fetch_interval_minutes: 5
    funds_fetch_interval_minutes: 10
    max_records_per_fetch: 1000
  
  # Rate limiting
  rate_limiting:
    requests_per_minute: 100
    burst_limit: 10

# ═══════════════════════════════════════════════════════════════════════════════
# 📥 DATA SOURCES CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
data_sources:
  # Execution agent integration
  execution_logs_path: "logs/execution_agent.log"
  execution_data_path: "data/execution"
  
  # Signal generation integration
  signals_path: "data/signals"
  signal_logs_path: "logs/signal_generation.log"
  
  # Market monitoring integration
  market_data_path: "data/market_monitoring"
  
  # Backtesting results
  backtest_results_path: "data/backtest"
  
  # Historical data
  historical_data_path: "data/historical"

# ═══════════════════════════════════════════════════════════════════════════════
# 🧮 ANALYSIS CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
analysis:
  # Analysis intervals
  analysis_interval_minutes: 15
  full_analysis_interval_hours: 6
  
  # Performance calculation settings
  initial_capital: 100000  # Rs 1,00,000
  risk_free_rate_annual: 0.06  # 6% annual risk-free rate
  
  # Strategy filtering
  min_trades_for_analysis: 5
  min_days_for_analysis: 7
  
  # Regime detection
  regime_detection:
    enabled: true
    lookback_days: 30
    volatility_threshold: 0.02
    trend_threshold: 0.1
  
  # Time-based analysis
  time_analysis:
    enabled: true
    morning_session: "09:15-12:00"
    afternoon_session: "12:00-15:25"
    
  # Drawdown analysis
  drawdown_analysis:
    max_drawdown_threshold_percent: 10
    drawdown_duration_threshold_days: 30

# ═══════════════════════════════════════════════════════════════════════════════
# 📤 REPORTING CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
reporting:
  # Report generation schedule
  daily_report:
    enabled: true
    generation_time: "16:00"  # 4:00 PM after market close
    
  weekly_report:
    enabled: true
    generation_day: "sunday"
    generation_time: "10:00"
    
  monthly_report:
    enabled: true
    generation_day: 1  # First day of month
    generation_time: "10:00"
  
  # Report formats
  formats:
    json: true
    csv: true
    parquet: true
    excel: false
  
  # Report content
  content:
    include_trade_details: true
    include_strategy_breakdown: true
    include_execution_quality: true
    include_equity_curves: true
    include_regime_analysis: true
    include_time_analysis: true
  
  # Performance thresholds for alerts
  alerts:
    min_roi_threshold: 5.0  # Minimum 5% ROI
    max_drawdown_threshold: 15.0  # Maximum 15% drawdown
    min_win_rate_threshold: 40.0  # Minimum 40% win rate
    min_profit_factor_threshold: 1.2  # Minimum 1.2 profit factor

# ═══════════════════════════════════════════════════════════════════════════════
# 💾 STORAGE CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
storage:
  # Data storage paths
  trade_data_path: "data/performance/trades"
  strategy_performance_path: "data/performance/strategies"
  execution_quality_path: "data/performance/execution"
  reports_path: "data/performance/reports"
  equity_curves_path: "data/performance/equity_curves"
  
  # File formats and compression
  file_format: "parquet"
  compression: "zstd"
  
  # Data retention
  retention:
    trade_data_days: 365  # Keep trade data for 1 year
    reports_days: 90      # Keep reports for 3 months
    logs_days: 30         # Keep logs for 1 month
  
  # Backup settings
  backup:
    enabled: true
    backup_path: "data/performance/backup"
    backup_interval_hours: 24
    max_backup_files: 7

# ═══════════════════════════════════════════════════════════════════════════════
# ⚡ PERFORMANCE CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
performance:
  # Processing settings
  processing:
    max_workers: 4
    chunk_size: 10000
    memory_limit_mb: 2048
  
  # Caching
  caching:
    enabled: true
    cache_size_mb: 512
    cache_ttl_minutes: 60
  
  # Optimization
  optimization:
    use_lazy_evaluation: true
    parallel_processing: true
    batch_processing: true

# ═══════════════════════════════════════════════════════════════════════════════
# 📝 LOGGING CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # File logging
  file_logging:
    enable: true
    filename: "performance_analysis.log"
    max_size_mb: 100
    backup_count: 5
  
  # Performance logging
  performance_logging:
    enable: true
    metrics_file: "logs/performance_metrics.log"
    interval_minutes: 5
  
  # Log directory
  log_directory: "logs"

# ═══════════════════════════════════════════════════════════════════════════════
# 🔗 INTEGRATIONS CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
integrations:
  # Signal Generation Agent
  signal_agent:
    enabled: true
    data_path: "data/signals"
    real_time_integration: true
    
  # Execution Agent
  execution_agent:
    enabled: true
    data_path: "data/execution"
    real_time_integration: true
    
  # Risk Management Agent
  risk_agent:
    enabled: true
    feedback_enabled: true
    performance_threshold_alerts: true
    
  # AI Training Agent
  ai_training_agent:
    enabled: true
    export_training_data: true
    training_data_path: "data/training"
    feature_export_interval_hours: 24
  
  # Market Monitoring Agent
  market_monitoring_agent:
    enabled: true
    regime_data_integration: true
    
  # Telegram notifications
  telegram:
    enabled: false
    bot_token: ""
    chat_id: ""
    
  # Email notifications
  email:
    enabled: false
    smtp_server: ""
    smtp_port: 587
    username: ""
    password: ""
    recipients: []

# ═══════════════════════════════════════════════════════════════════════════════
# 🚨 ERROR HANDLING CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
error_handling:
  # Retry settings
  max_retries: 3
  retry_delay_seconds: 5
  exponential_backoff: true
  
  # Error notifications
  notify_on_errors: true
  critical_error_threshold: 5
  
  # Recovery settings
  auto_recovery: true
  recovery_timeout_minutes: 30
  
  # Graceful degradation
  fallback_mode: true
  minimal_functionality: true
